{"build_type": "release", "env_name": "esp32dev", "libsource_dirs": ["C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\lib", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev", "C:\\Users\\<USER>\\.platformio\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries"], "defines": ["PLATFORMIO=60118", "ARDUINO_ESP32_DEV", "HAVE_CONFIG_H", "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"", "UNITY_INCLUDE_CONFIG_H", "WITH_POSIX", "_GNU_SOURCE", "IDF_VER=\"v4.4.7-dirty\"", "ESP_PLATFORM", "_POSIX_READER_WRITER_LOCKS", "ARDUINO_ARCH_ESP32", "ESP32", "F_CPU=240000000L", "ARDUINO=10812", "ARDUINO_VARIANT=\"esp32\"", "ARDUINO_BOARD=\"Espressif ESP32 Dev Module\"", "ARDUINO_PARTITION_default"], "includes": {"build": ["C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\include", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\src", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\WebSockets\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFiClientSecure\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFi\\src", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\Adafruit ADS1X15", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\Adafruit BusIO", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Wire\\src", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\ArduinoJson\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\newlib\\platform_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\freertos\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\freertos\\include\\esp_additions\\freertos", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\freertos\\port\\xtensa\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\freertos\\include\\esp_additions", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hw_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hw_support\\include\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hw_support\\include\\soc\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hw_support\\port\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hw_support\\port\\esp32\\private_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\heap\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\log\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\lwip\\include\\apps", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\lwip\\include\\apps\\sntp", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\lwip\\lwip\\src\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\lwip\\port\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\lwip\\port\\esp32\\include\\arch", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\soc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\soc\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\soc\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\hal\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\hal\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\hal\\platform_port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_rom\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_rom\\include\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_rom\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_system\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_system\\port\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_system\\port\\public_compat", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\xtensa\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\xtensa\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\driver\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\driver\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_pm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_ringbuf\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\efuse\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\efuse\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\vfs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_wifi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_event\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_netif\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_eth\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\tcpip_adapter\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_phy\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_phy\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_ipc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\app_trace\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_timer\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\mbedtls\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\mbedtls\\mbedtls\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\mbedtls\\esp_crt_bundle\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\app_update\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\spi_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bootloader_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\nvs_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\pthread\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_gdbstub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_gdbstub\\xtensa", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_gdbstub\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espcoredump\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espcoredump\\include\\port\\xtensa", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\wpa_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\wpa_supplicant\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\wpa_supplicant\\esp_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\ieee802154\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\console", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\asio\\asio\\asio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\asio\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\common\\osi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\include\\esp32\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\common\\api\\include\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\common\\btc\\profile\\esp\\blufi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\common\\btc\\profile\\esp\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\host\\bluedroid\\api\\include\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_common\\tinycrypt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_core", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_core\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_core\\storage", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\btc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_models\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_models\\client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\mesh_models\\server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\api\\core\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\api\\models\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\bt\\esp_ble_mesh\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\cbor\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\unity\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\unity\\unity\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\cmock\\CMock\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\coap\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\coap\\libcoap\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\nghttp\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\nghttp\\nghttp2\\lib\\includes", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-tls", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-tls\\esp-tls-crypto", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_adc_cal\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_hid\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\tcp_transport\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_http_client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_http_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_https_ota\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_https_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_lcd\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_lcd\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\protobuf-c\\protobuf-c", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\protocomm\\include\\common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\protocomm\\include\\security", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\protocomm\\include\\transports", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\mdns\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_local_ctrl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\sdmmc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_serial_slave_link\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_websocket_client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\expat\\expat\\expat\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\expat\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\wear_levelling\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\fatfs\\diskio", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\fatfs\\vfs", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\fatfs\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\freemodbus\\freemodbus\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\idf_test\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\idf_test\\include\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\jsmn\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\json\\cJSON", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\libsodium\\libsodium\\src\\libsodium\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\libsodium\\port_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\mqtt\\esp-mqtt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\openssl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\perfmon\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\spiffs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\ulp\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\wifi_provisioning\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\rmaker_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_diagnostics\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\rtc_store\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_insights\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\json_parser\\upstream\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\json_parser\\upstream", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\json_generator\\upstream", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_schedule\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp_secure_cert_mgr\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_rainmaker\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\gpio_button\\button\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\qrcode\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\ws2812_led", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp_littlefs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\tool", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\typedef", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\image", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\math", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\nn", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\layer", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\detect", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp-dl\\include\\model_zoo", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp32-camera\\driver\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\esp32-camera\\conversions\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\dotprod\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\support\\mem\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\hann\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\blackman\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\blackman_harris\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\blackman_nuttall\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\nuttall\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\windows\\flat_top\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\iir\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\fir\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\add\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\sub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\mul\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\addc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\mulc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\math\\sqrt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\mul\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\add\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\addc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\mulc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\sub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\fft\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\dct\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\conv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\matrix\\mul\\test\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\kalman\\ekf\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\espressif__esp-dsp\\modules\\kalman\\ekf_imu13states\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\include\\fb_gfx\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\sdk\\esp32\\dio_qspi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\cores\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\variants\\esp32"], "compatlib": ["C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\Adafruit ADS1X15", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\Adafruit BusIO", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\ArduinoJson\\src", "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\libdeps\\esp32dev\\WebSockets\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFiClientSecure\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFiClientSecure\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Wire\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ArduinoOTA\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\AsyncUDP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\BLE\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\BluetoothSerial\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\DNSServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\EEPROM\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESP32\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESPmDNS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\FFat\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\FS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPClient\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPUpdate\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPUpdateServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\I2S\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Insights\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\LittleFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\NetBIOS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Preferences\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\RainMaker\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SD\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SD_MMC\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPIFFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SimpleBLE\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ticker\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\USB\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Update\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFiProv\\src"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\xtensa-esp32-elf\\include\\c++\\8.4.0", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\xtensa-esp32-elf\\include\\c++\\8.4.0\\xtensa-esp32-elf", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\lib\\gcc\\xtensa-esp32-elf\\8.4.0\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\lib\\gcc\\xtensa-esp32-elf\\8.4.0\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\xtensa-esp32-elf\\include"]}, "cc_flags": ["-Wno-frame-address", "-std=gnu99", "-Wno-old-style-declaration", "-<PERSON><PERSON>", "-mlongcalls", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-ggdb", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-Wno-error=unused-but-set-variable", "-fno-jump-tables", "-fno-tree-switch-conversion", "-M<PERSON>"], "cxx_flags": ["-Wno-frame-address", "-std=gnu++11", "-fexceptions", "-fno-rtti", "-<PERSON><PERSON>", "-mlongcalls", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-ggdb", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-Wno-error=unused-but-set-variable", "-fno-jump-tables", "-fno-tree-switch-conversion", "-M<PERSON>"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\bin\\xtensa-esp32-elf-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\bin\\xtensa-esp32-elf-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa-esp32\\bin\\xtensa-esp32-elf-gdb.exe", "prog_path": "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\build\\esp32dev\\firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}], "extra": {"flash_images": [{"offset": "0x1000", "path": "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\build\\esp32dev\\bootloader.bin"}, {"offset": "0x8000", "path": "C:\\Users\\<USER>\\OneDrive\\Documents\\PlatformIO\\Projects\\adc\\.pio\\build\\esp32dev\\partitions.bin"}, {"offset": "0xe000", "path": "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\partitions\\boot_app0.bin"}], "application_offset": "0x10000"}}