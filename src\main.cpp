#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_ADS1X15.h>
#include <WiFi.h>
#include <WebSocketsClient.h>
#include <ArduinoJson.h>

const char* ssid = "192 HP";
const char* password = "0983353350";
const char* ws_host = "************";
const int ws_port = 80;
const char* ws_url = "/ws";

// ===== PIN DEFINITIONS =====
#define POWER_PIN 14
#define HEATER_PIN 26
#define FAN_PIN 27
#define DAC_PIN 25  // DAC pin for ~100mV signal generation
//#define LED_PIN 2   // Built-in LED for status indication

// ===== TIMING CONSTANTS =====
const unsigned long SENSOR_INTERVAL = 500;       // 500ms sensor reading interval for real-time display (2Hz)
const unsigned long DATA_LOG_INTERVAL = 5000;    // 5s interval for detailed data logging and charts (0.2Hz)
const unsigned long WIFI_TIMEOUT = 30000;        // 30s WiFi connection timeout
const unsigned long RECONNECT_INTERVAL = 5000;   // 5s WebSocket reconnect interval

struct DeviceData {
  bool powerState;
  bool heaterState;
  bool fanState;
};

DeviceData deviceData = { false, false, false };
WebSocketsClient webSocket;
bool isConnected = false;
unsigned long lastSensorUpdate = 0;
unsigned long lastDataLog = 0;

Adafruit_ADS1115 ads;

void handleWebSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
  switch (type) {
    case WStype_DISCONNECTED:
      Serial.println("[WSc] Không kết nối được web!");
      isConnected = false;
      break;
    case WStype_CONNECTED:
      Serial.println("[WSc] Đã kết nối với Web!");
      isConnected = true;
      webSocket.sendTXT("{\"type\":\"esp32_connect\",\"deviceId\":\"ESP32_001\"}");
      break;
    case WStype_TEXT: {
      Serial.printf("[WSc] Received: %s\n", payload);
      StaticJsonDocument<200> doc;
      DeserializationError error = deserializeJson(doc, payload);
      if (error) {
        Serial.println("Parse error");
        return;
      }
      const char* action = doc["action"];
      Serial.printf("[WSc] Action: %s\n", action);

      if (strcmp(action, "power_on") == 0) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật On/Off
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật On/Off
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật On/Off
        deviceData.powerState = true;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "power_off") == 0) {
        digitalWrite(POWER_PIN, LOW);   // GPIO14 LOW khi tắt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt
        deviceData.powerState = false;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power OFF - GPIO14:LOW, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật bếp
        digitalWrite(HEATER_PIN, HIGH); // GPIO26 HIGH khi bật bếp
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật bếp
        deviceData.heaterState = true;
        deviceData.fanState = false;
        Serial.println("[WSc] Heater ON - GPIO14:HIGH, GPIO26:HIGH, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_off") == 0 && deviceData.powerState) {
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt bếp
        deviceData.heaterState = false;
        Serial.println("[WSc] Heater OFF - GPIO26:LOW");
      }
      else if (strcmp(action, "fan_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật quạt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật quạt
        digitalWrite(FAN_PIN, HIGH);    // GPIO27 HIGH khi bật quạt
        deviceData.fanState = true;
        deviceData.heaterState = false;
        Serial.println("[WSc] Fan ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:HIGH");
      }
      else if (strcmp(action, "fan_off") == 0 && deviceData.powerState) {
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt quạt
        deviceData.fanState = false;
        Serial.println("[WSc] Fan OFF - GPIO27:LOW");
      }
      else {
        Serial.println("[WSc] Unknown action");
        return;
      }

      StaticJsonDocument<200> stateDoc;
      stateDoc["type"] = "state_update";
      stateDoc["power"] = deviceData.powerState;
      stateDoc["heater"] = deviceData.heaterState;
      stateDoc["fan"] = deviceData.fanState;

      String stateJson;
      serializeJson(stateDoc, stateJson);
      webSocket.sendTXT(stateJson);
      Serial.printf("[WSc] Sent: %s\n", stateJson.c_str());
      break;
    }
  }
}

// --- PHẦN ĐO ADC ĐƯỢC CHỈNH SỬA VỚI DIGITAL FILTERING ---
// Moving average filter để giảm nhiễu
class MovingAverageFilter {
private:
  float* buffer;
  int size;
  int index;
  float sum;
  bool filled;

public:
  MovingAverageFilter(int filterSize) {
    size = filterSize;
    buffer = new float[size];
    index = 0;
    sum = 0;
    filled = false;
    for (int i = 0; i < size; i++) {
      buffer[i] = 0;
    }
  }

  float addValue(float value) {
    sum -= buffer[index];
    buffer[index] = value;
    sum += value;
    index = (index + 1) % size;
    if (index == 0) filled = true;

    return sum / (filled ? size : (index == 0 ? size : index));
  }
};

// Khởi tạo filters cho mỗi channel - tối ưu cho thí nghiệm Curie
MovingAverageFilter currentFilter(3);  // 3-point moving average cho current (1.5s history @ 2Hz)
MovingAverageFilter voltageFilter(3);  // 3-point moving average cho voltage (1.5s history @ 2Hz)

// Hàm đo dữ liệu cảm biến tối ưu cho độ chính xác cao nhất
void measureSensorData(float &Uc, float &current, bool highAccuracy = true) {
  // Cấu hình tối ưu cho độ chính xác so với đồng hồ vạn năng
  // LUÔN DÙNG SINGLE-SHOT MODE để đạt độ chính xác cao nhất
  int samples, settling_delay;
  bool useOutlierRejection;

  if (highAccuracy) {
    // CHÍNH XÁC TỐI ĐA - cho data logging (ưu tiên độ chính xác)
    samples = 10;           // Tăng samples để giảm nhiễu
    settling_delay = 20;    // Tăng settling time để ADC ổn định hoàn toàn
    useOutlierRejection = true;
  } else {
    // REAL-TIME - cho hiển thị (cân bằng tốc độ và chính xác)
    samples = 5;
    settling_delay = 12;
    useOutlierRejection = false;
  }

  // === ĐO CURRENT QUA MICRO AMMETER (A0-A1) ===
  // Đảm bảo ADC ở trạng thái ổn định trước khi đo
  delay(5); // Pre-settling delay

  float voltageDiff = 0;
  float validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Single-shot conversion cho độ chính xác cao nhất
    int16_t diff_0_1 = ads.readADC_Differential_0_1(); // A0 - A1 differential

    float rawValue = diff_0_1 * 7.8125; // µV với GAIN_SIXTEEN

    // Áp dụng outlier rejection chỉ khi cần chính xác cao
    if (useOutlierRejection) {
      // Giới hạn phù hợp với dải đo thực tế: 50µA × 5000Ω = 250mV
      if (abs(rawValue) < 300000) { // Giới hạn ±300mV để chứa tín hiệu hợp lệ
        voltageDiff += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - giới hạn rộng hơn
      if (abs(rawValue) < 400000) { // ±400mV cho real-time
        voltageDiff += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay); // Đợi ADC ổn định hoàn toàn
  }

  if (validSamples > 0) {
    voltageDiff /= validSamples; // Trung bình chỉ các mẫu hợp lệ
  }

  // Áp dụng moving average filter - adaptive cho thí nghiệm Curie
  float rawCurrent = voltageDiff;
  if (highAccuracy) {
    // Data logging: Dùng full filter cho độ chính xác
    voltageDiff = currentFilter.addValue(voltageDiff);
  } else {
    // Real-time: Giảm filtering để phản ứng nhanh hơn
    voltageDiff = (rawCurrent + voltageDiff) / 2.0; // Simple 2-point average
  }

  // Debug info cho lần thử nghiệm đầu
  if (highAccuracy) {
    Serial.printf("[DEBUG] Current: Raw=%.1fµV, Filtered=%.1fµV, Samples=%d/%d\n",
                  rawCurrent, voltageDiff, (int)validSamples, samples);
  }

  // Shunt resistor cho micro ammeter
  // Tính toán: 50µA × 5000Ω = 250mV (< 256mV dải ADC) ✓
  const float R_SHUNT = 5000.0;  // 5kΩ shunt resistor
  current = voltageDiff / R_SHUNT; // µA = µV / Ohm

  // Delay dài khi chuyển channel để tránh crosstalk hoàn toàn
  delay(highAccuracy ? 100 : 50); // Delay dài hơn cho độ chính xác cao

  // === ĐO THERMOCOUPLE (A2-A3) ===
  // Đảm bảo ADC ổn định sau khi chuyển channel
  delay(10); // Channel switching settling

  float tempVoltage = 0;
  validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Single-shot conversion cho độ chính xác cao nhất
    int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential

    float rawValue = diff_2_3 * 0.0078125; // mV với GAIN_SIXTEEN

    if (useOutlierRejection) {
      // Giới hạn chặt chẽ cho thermocouple (dải Curie: 0-35mV)
      if (rawValue >= -2 && rawValue <= 40) { // Dải hợp lý cho thí nghiệm Curie
        tempVoltage += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - dải rộng hơn
      if (rawValue >= -5 && rawValue <= 50) {
        tempVoltage += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay);
  }

  if (validSamples > 0) {
    tempVoltage /= validSamples;
  }

  // Áp dụng moving average filter
  float rawVoltage = tempVoltage;
  Uc = voltageFilter.addValue(tempVoltage);

  // Debug info cho lần thử nghiệm đầu
  if (highAccuracy) {
    Serial.printf("[DEBUG] Thermocouple: Raw=%.3fmV, Filtered=%.3fmV, Samples=%d/%d\n",
                  rawVoltage, Uc, (int)validSamples, samples);
  }

  // === PHẦN ĐO 4-5V (COMMENT) ===
  // // Đo điện áp dải 4-5V: A2=dương, A3=âm
  // float Uc = 0;
  // for (int i = 0; i < samples; i++) {
  //   int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential
  //   Uc += diff_2_3 * 0.125; // mV với GAIN_ONE (125µV/bit)
  //   delay(5);
  // }
  // Uc /= samples; // Trung bình mV
  // Uc = Uc / 1000.0; // Chuyển từ mV sang V để hiển thị
}

// Gửi dữ liệu real-time cho index (cập nhật liên tục)
void sendRealtimeData() {
  if (!isConnected || !deviceData.powerState) return;

  float Uc, current;
  measureSensorData(Uc, current, false); // false = real-time (cân bằng tốc độ/chính xác)

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "realtime_data";  // Loại dữ liệu real-time
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  serializeJson(doc, json);
  webSocket.sendTXT(json);
  Serial.printf("[Real-time 2Hz] Uc=%.3fmV, I=%.3fuA at %lu ms\n", Uc, current, recordTime);
}

// Gửi dữ liệu chi tiết cho logging và đồ thị (mỗi 10s)
void sendDataLog() {
  if (!isConnected || !deviceData.powerState) return;

  float Uc, current;
  measureSensorData(Uc, current, true); // true = chính xác cao

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "data_log";  // Loại dữ liệu cho chi tiết và đồ thị
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  serializeJson(doc, json);
  webSocket.sendTXT(json);
  Serial.printf("[Data Log 0.2Hz] Uc=%.3fmV, I=%.3fuA logged at %lu ms\n", Uc, current, recordTime);
}



void setup() {
  Serial.begin(115200);

  pinMode(POWER_PIN, OUTPUT);
  pinMode(HEATER_PIN, OUTPUT);
  pinMode(FAN_PIN, OUTPUT);
  // Khởi tạo tất cả GPIO ở LOW (trạng thái ban đầu tắt)
  digitalWrite(POWER_PIN, LOW);
  digitalWrite(HEATER_PIN, LOW);
  digitalWrite(FAN_PIN, LOW);

  // Cấu hình DAC cho chân GPIO25: tạo tín hiệu khoảng 100 mV
  dacWrite(DAC_PIN, 8); // Tính theo: 3.3V * 8/255 ≈ 0.1035V

  if (!ads.begin()) {
    Serial.println("Lỗi khởi tạo ADS1115!");
    while (1);
  }
  // Cấu hình cho cả micro ammeter và thermocouple
  ads.setGain(GAIN_SIXTEEN); // ±0.256V, 7.8125µV/bit - tối ưu cho thermocouple 0-35mV và micro ammeter 0-50µA

  // Validation cấu hình cho thí nghiệm Curie
  Serial.println("=== ADS1115 Configuration ===");
  Serial.println("GAIN: GAIN_SIXTEEN (±256mV, 7.8125µV/bit)");
  Serial.println("Current range: 0-50µA via 5kΩ shunt (0-250mV)");
  Serial.println("Thermocouple range: 0-35mV (0-800°C K-type)");
  Serial.println("Real-time: 500ms (2Hz)");
  Serial.println("Data log: 5s (0.2Hz)");
  Serial.println("=============================");
  WiFi.begin(ssid, password);
  Serial.print("Đang kết nối WiFi...");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nĐã kết nối WiFi!");

  webSocket.begin(ws_host, ws_port, ws_url);
  webSocket.onEvent(handleWebSocketEvent);
  webSocket.setReconnectInterval(5000);
}

void loop() {
  webSocket.loop();
  unsigned long currentTime = millis();

  if (isConnected && deviceData.powerState) {
    // Gửi dữ liệu khi có nguồn, không cần bật bếp/quạt

    // Gửi dữ liệu real-time mỗi 500ms (2Hz) cho index
    if (currentTime - lastSensorUpdate >= SENSOR_INTERVAL) {
      sendRealtimeData();
      lastSensorUpdate = currentTime;
    }

    // Gửi dữ liệu chi tiết mỗi 5s (0.2Hz) cho logging và đồ thị
    if (currentTime - lastDataLog >= DATA_LOG_INTERVAL) {
      sendDataLog();
      lastDataLog = currentTime;
    }
  }
}
