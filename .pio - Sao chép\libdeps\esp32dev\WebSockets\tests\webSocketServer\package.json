{"name": "webSocketServer", "version": "1.0.0", "description": "WebSocketServer for testing", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/Links2004/arduinoWebSockets"}, "keywords": ["esp8266", "websocket", "a<PERSON><PERSON><PERSON>"], "author": "<PERSON>", "license": "LGPLv2", "bugs": {"url": "https://github.com/Links2004/arduinoWebSockets/issues"}, "homepage": "https://github.com/Links2004/arduinoWebSockets", "dependencies": {"websocket": "^1.0.18"}}