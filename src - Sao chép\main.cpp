#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_ADS1X15.h>
#include <WiFi.h>
#include <WebSocketsClient.h>
#include <ArduinoJson.h>

const char* ssid = "192 HP";
const char* password = "0983353350";
const char* ws_host = "***********";
const int ws_port = 80;
const char* ws_url = "/ws";

// ===== PIN DEFINITIONS =====
#define POWER_PIN 14
#define HEATER_PIN 26
#define FAN_PIN 27
#define DAC_PIN 25  // DAC pin for ~100mV signal generation
//#define LED_PIN 2   // Built-in LED for status indication

// ===== TIMING CONSTANTS =====
const unsigned long SENSOR_INTERVAL = 100;       // 100ms sensor reading interval for real-time display (10Hz)
const unsigned long DATA_LOG_INTERVAL = 10000;   // 10s interval for detailed data logging and charts (0.1Hz)
const unsigned long WIFI_TIMEOUT = 30000;        // 30s WiFi connection timeout
const unsigned long RECONNECT_INTERVAL = 5000;   // 5s WebSocket reconnect interval

struct DeviceData {
  bool powerState;
  bool heaterState;
  bool fanState;
};

DeviceData deviceData = { false, false, false };
WebSocketsClient webSocket;
bool isConnected = false;
unsigned long lastSensorUpdate = 0;
unsigned long lastDataLog = 0;

// FIX RACE CONDITION: Thêm flags để tránh gọi đồng thời
bool isMeasuring = false;

Adafruit_ADS1115 ads;

void handleWebSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
  switch (type) {
    case WStype_DISCONNECTED:
      Serial.println("[WSc] Không kết nối được web!");
      isConnected = false;
      break;
    case WStype_CONNECTED:
      Serial.println("[WSc] Đã kết nối với Web!");
      isConnected = true;
      webSocket.sendTXT("{\"type\":\"esp32_connect\",\"deviceId\":\"ESP32_001\"}");
      break;
    case WStype_TEXT: {
      Serial.printf("[WSc] Received: %s\n", payload);
      StaticJsonDocument<200> doc;
      DeserializationError error = deserializeJson(doc, payload);
      if (error) {
        Serial.println("Parse error");
        return;
      }
      const char* action = doc["action"];
      Serial.printf("[WSc] Action: %s\n", action);

      if (strcmp(action, "power_on") == 0) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật On/Off
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật On/Off
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật On/Off
        deviceData.powerState = true;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "power_off") == 0) {
        digitalWrite(POWER_PIN, LOW);   // GPIO14 LOW khi tắt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt
        deviceData.powerState = false;
        deviceData.heaterState = false;
        deviceData.fanState = false;
        Serial.println("[WSc] Power OFF - GPIO14:LOW, GPIO26:LOW, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật bếp
        digitalWrite(HEATER_PIN, HIGH); // GPIO26 HIGH khi bật bếp
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi bật bếp
        deviceData.heaterState = true;
        deviceData.fanState = false;
        Serial.println("[WSc] Heater ON - GPIO14:HIGH, GPIO26:HIGH, GPIO27:LOW");
      }
      else if (strcmp(action, "heater_off") == 0 && deviceData.powerState) {
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi tắt bếp
        deviceData.heaterState = false;
        Serial.println("[WSc] Heater OFF - GPIO26:LOW");
      }
      else if (strcmp(action, "fan_on") == 0 && deviceData.powerState) {
        digitalWrite(POWER_PIN, HIGH);  // GPIO14 HIGH khi bật quạt
        digitalWrite(HEATER_PIN, LOW);  // GPIO26 LOW khi bật quạt
        digitalWrite(FAN_PIN, HIGH);    // GPIO27 HIGH khi bật quạt
        deviceData.fanState = true;
        deviceData.heaterState = false;
        Serial.println("[WSc] Fan ON - GPIO14:HIGH, GPIO26:LOW, GPIO27:HIGH");
      }
      else if (strcmp(action, "fan_off") == 0 && deviceData.powerState) {
        digitalWrite(FAN_PIN, LOW);     // GPIO27 LOW khi tắt quạt
        deviceData.fanState = false;
        Serial.println("[WSc] Fan OFF - GPIO27:LOW");
      }
      else {
        Serial.println("[WSc] Unknown action");
        return;
      }

      StaticJsonDocument<200> stateDoc;
      stateDoc["type"] = "state_update";
      stateDoc["power"] = deviceData.powerState;
      stateDoc["heater"] = deviceData.heaterState;
      stateDoc["fan"] = deviceData.fanState;

      String stateJson;
      serializeJson(stateDoc, stateJson);
      webSocket.sendTXT(stateJson);
      Serial.printf("[WSc] Sent: %s\n", stateJson.c_str());
      break;
    }
  }
}

// --- PHẦN ĐO ADC ĐÃ TỐI ƯU VỚI BỘ LỌC SỐ ---
// Bộ lọc trung bình trượt để giảm nhiễu
class MovingAverageFilter {
private:
  float* buffer;
  int size;
  int index;
  float sum;
  bool filled;

public:
  MovingAverageFilter(int filterSize) {
    size = filterSize;
    buffer = new float[size];
    index = 0;
    sum = 0;
    filled = false;
    for (int i = 0; i < size; i++) {
      buffer[i] = 0;
    }
  }

  // FIX MEMORY LEAK: Thêm destructor
  ~MovingAverageFilter() {
    delete[] buffer;
    buffer = nullptr;
  }

  float addValue(float value) {
    sum -= buffer[index];
    buffer[index] = value;
    sum += value;
    index = (index + 1) % size;
    if (index == 0) filled = true;

    return sum / (filled ? size : (index == 0 ? size : index));
  }

  // Đặt lại bộ lọc khi có thay đổi đột ngột (ví dụ: tắt/bật máy)
  void reset(float giaTriKhoiTao = 0) {
    for (int i = 0; i < size; i++) {
      buffer[i] = giaTriKhoiTao;
    }
    sum = giaTriKhoiTao * size;
    index = 0;
    filled = false;
  }
};

// Khởi tạo bộ lọc cho mỗi kênh - tối ưu cho thí nghiệm Curie
MovingAverageFilter boLocDong(3);      // Bộ lọc 3 điểm cho dòng điện (lịch sử 1.5s @ 2Hz)
MovingAverageFilter boLocDienAp(3);    // Bộ lọc 3 điểm cho điện áp (lịch sử 1.5s @ 2Hz)

// Hàm đo dữ liệu cảm biến tối ưu cho độ chính xác cao nhất
void measureSensorData(float &Uc, float &current, bool highAccuracy = true) {
  // Cấu hình tối ưu cho độ chính xác so với đồng hồ vạn năng
  // LUÔN DÙNG CHẾ ĐỘ ĐO ĐƠN để đạt độ chính xác cao nhất
  int samples, settling_delay;
  bool useOutlierRejection;

  if (highAccuracy) {
    // CHÍNH XÁC TỐI ĐA - cho ghi dữ liệu (ưu tiên độ chính xác gần đồng hồ vạn năng)
    samples = 16;                    // 16 samples = giảm noise 4x, thời gian tối ưu ~80ms
    settling_delay = 5;              // 5ms đủ cho ADS1115 ổn định (>4x conversion time)
    useOutlierRejection = true;
  } else {
    // THỜI GIAN THỰC - cho hiển thị (cân bằng tốc độ và chính xác tối ưu)
    samples = 8;                     // 8 samples = giảm noise 2.8x, thời gian ~40ms
    settling_delay = 5;              // Cùng settling time để consistency
    useOutlierRejection = true;      // Luôn dùng outlier rejection để loại bỏ spike
  }

  // === ĐO DÒNG ĐIỆN QUA MICRO AMMETER (A0-A1) ===
  // Đảm bảo ADC ở trạng thái ổn định trước khi đo
  delay(5); // Thời gian ổn định trước khi đo

  float voltageDiff = 0;
  float validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Single-shot conversion cho độ chính xác cao nhất
    int16_t diff_0_1 = ads.readADC_Differential_0_1(); // A0 - A1 differential

    float rawValue = diff_0_1 * 7.8125; // µV với GAIN_SIXTEEN

    // Áp dụng outlier rejection chỉ khi cần chính xác cao
    if (useOutlierRejection) {
      // Giới hạn phù hợp với dải đo thực tế: 50µA × 5000Ω = 250mV
      if (abs(rawValue) < 300000) { // Giới hạn ±300mV để chứa tín hiệu hợp lệ
        voltageDiff += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - giới hạn rộng hơn
      if (abs(rawValue) < 400000) { // ±400mV cho real-time
        voltageDiff += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay); // Đợi ADC ổn định hoàn toàn
  }

  if (validSamples > 0) {
    voltageDiff /= validSamples; // Trung bình chỉ các mẫu hợp lệ
  } else {
    // Không có mẫu hợp lệ - báo lỗi và dùng giá trị 0
    Serial.println("[LỖI] Không có mẫu dòng điện hợp lệ!");
    voltageDiff = 0;
  }

  // Adaptive filtering cho thí nghiệm Curie - ưu tiên theo dõi thay đổi thực
  float rawCurrent = voltageDiff;
  if (highAccuracy) {
    // Data logging: Dùng filter để giảm noise, đảm bảo độ chính xác cao
    voltageDiff = boLocDong.addValue(voltageDiff);
  } else {
    // Real-time: KHÔNG FILTER - hiển thị thay đổi thực ngay lập tức
    // Điều này đảm bảo theo dõi được thay đổi dòng điện tức thì
    voltageDiff = rawCurrent; // RAW DATA - không filter để không mất thay đổi
  }

  // Debug info cho lần thử nghiệm đầu
  if (highAccuracy) {
    Serial.printf("[DEBUG] Current: Raw=%.1fµV, Filtered=%.1fµV, Samples=%d/%d\n",
                  rawCurrent, voltageDiff, (int)validSamples, samples);
  }

  // Shunt resistor cho micro ammeter
  // Tính toán: 50µA × 5000Ω = 250mV (< 256mV dải ADC) ✓
  const float R_SHUNT = 5000.0;  // 5kΩ shunt resistor
  current = voltageDiff / R_SHUNT; // µA = µV / Ohm

  // Delay tối ưu khi chuyển channel để tránh crosstalk
  delay(10); // 10ms đủ cho channel switching, tối ưu speed vs accuracy

  // === ĐO THERMOCOUPLE (A2-A3) ===
  // Đảm bảo ADC ổn định sau khi chuyển channel
  delay(10); // Channel switching settling

  float tempVoltage = 0;
  validSamples = 0;

  for (int i = 0; i < samples; i++) {
    // Single-shot conversion cho độ chính xác cao nhất
    int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential

    float rawValue = diff_2_3 * 0.0078125; // mV với GAIN_SIXTEEN

    if (useOutlierRejection) {
      // Giới hạn rộng hơn để không loại bỏ thay đổi thực của thí nghiệm
      if (rawValue >= -5 && rawValue <= 50) { // Dải rộng cho thí nghiệm Curie
        tempVoltage += rawValue;
        validSamples++;
      }
    } else {
      // Chế độ real-time - chấp nhận hầu hết giá trị để theo dõi thay đổi
      if (rawValue >= -10 && rawValue <= 60) { // Dải rất rộng
        tempVoltage += rawValue;
        validSamples++;
      }
    }
    delay(settling_delay);
  }

  if (validSamples > 0) {
    tempVoltage /= validSamples;
  } else {
    // Không có mẫu hợp lệ - báo lỗi và dùng giá trị 0
    Serial.println("[LỖI] Không có mẫu thermocouple hợp lệ!");
    tempVoltage = 0;
  }

  // Adaptive filtering với phát hiện thay đổi đột ngột
  float rawVoltage = tempVoltage;
  static float lastRawVoltage = 0.0; // FIX: Khởi tạo với giá trị constant
  static unsigned long lastMeasureTime = 0; // FIX: Khởi tạo với 0
  static bool firstRun = true;

  // Phát hiện thay đổi đột ngột dựa trên thời gian và mức độ thay đổi
  unsigned long currentTime = millis();
  unsigned long timeDiff;

  // Xử lý overflow của millis() (sau ~49 ngày)
  if (currentTime >= lastMeasureTime) {
    timeDiff = currentTime - lastMeasureTime;
  } else {
    // Overflow đã xảy ra
    timeDiff = (0xFFFFFFFF - lastMeasureTime) + currentTime + 1;
  }
  float change = abs(rawVoltage - lastRawVoltage);

  // Ngưỡng thích ứng cho reset filter - CHỈ cho thay đổi đột ngột thực sự
  // - Thay đổi lớn (>5mV): Chắc chắn là tắt/bật máy hoặc thay đổi đột ngột
  // - Thời gian dài (>30s): Có thể là drift, cần reset
  float threshold = 5.0; // 5mV - chỉ reset khi thay đổi thực sự lớn
  bool shouldReset = false;

  if (firstRun) {
    shouldReset = true;
    Serial.println("[RESET] First run - initializing filters");
  } else if (change > threshold) {
    shouldReset = true;
    Serial.printf("[RESET] Large change detected: %.3f→%.3fmV (Δ%.3f) - likely power cycle\n",
                  lastRawVoltage, rawVoltage, rawVoltage - lastRawVoltage);
  } else if (timeDiff > 30000 && change > 1.0) {
    shouldReset = true;
    Serial.printf("[RESET] Long-term drift detected: %.3f→%.3fmV after %lums\n",
                  lastRawVoltage, rawVoltage, timeDiff);
  }

  if (shouldReset) {
    boLocDienAp.reset(rawVoltage); // Đặt lại bộ lọc điện áp với giá trị hiện tại
    boLocDong.reset(0); // Đặt lại bộ lọc dòng điện
    firstRun = false;
  }

  lastMeasureTime = currentTime;

  if (highAccuracy) {
    // Data logging: Dùng filter để giảm noise, đã được reset khi cần
    Uc = boLocDienAp.addValue(tempVoltage);
  } else {
    // Real-time: KHÔNG FILTER - hiển thị thay đổi thực ngay lập tức
    // Điều này đảm bảo theo dõi được thay đổi chậm như 0.55→0.87→1.1mV
    Uc = rawVoltage; // RAW DATA - không filter để không mất thay đổi
  }

  lastRawVoltage = rawVoltage;

  // Debug info cho lần thử nghiệm đầu - theo dõi thay đổi
  if (highAccuracy) {
    Serial.printf("[DEBUG] Thermocouple: Raw=%.3fmV, Filtered=%.3fmV, Samples=%d/%d\n",
                  rawVoltage, Uc, (int)validSamples, samples);
  } else {
    // Real-time debug - hiển thị thay đổi
    static float lastRaw = 0;
    static bool firstDebugRun = true;
    if (firstDebugRun) {
      lastRaw = rawVoltage;
      firstDebugRun = false;
    }
    float change = rawVoltage - lastRaw;
    if (abs(change) > 0.01) { // Chỉ hiển thị khi có thay đổi > 0.01mV
      Serial.printf("[THAY_DOI] Raw: %.3f→%.3fmV (Δ%.3f), Hiển thị: %.3fmV\n",
                    lastRaw, rawVoltage, change, Uc);
    }
    lastRaw = rawVoltage;
  }

  // === PHẦN ĐO 4-5V (COMMENT) ===
  // // Đo điện áp dải 4-5V: A2=dương, A3=âm
  // float Uc = 0;
  // for (int i = 0; i < samples; i++) {
  //   int16_t diff_2_3 = ads.readADC_Differential_2_3(); // A2 - A3 differential
  //   Uc += diff_2_3 * 0.125; // mV với GAIN_ONE (125µV/bit)
  //   delay(5);
  // }
  // Uc /= samples; // Trung bình mV
  // Uc = Uc / 1000.0; // Chuyển từ mV sang V để hiển thị
}

// Gửi dữ liệu real-time cho index (cập nhật liên tục)
void sendRealtimeData() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, false); // false = real-time (không filter, thay đổi tức thì)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Real-time data invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "realtime_data";  // Loại dữ liệu real-time
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Real-time 10Hz] Uc=%.3fmV, I=%.3fuA at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho real-time data!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}

// Gửi dữ liệu chi tiết cho logging và đồ thị (mỗi 10s)
void sendDataLog() {
  if (!isConnected || !deviceData.powerState || isMeasuring) return;

  // FIX RACE CONDITION: Set flag để tránh gọi đồng thời
  isMeasuring = true;

  // Đảm bảo đo xong mới gửi - không có race condition
  float Uc, current;
  measureSensorData(Uc, current, true); // true = chính xác cao (có filter)

  // Kiểm tra dữ liệu hợp lệ trước khi gửi
  if (isnan(Uc) || isnan(current)) {
    Serial.println("[LỖI] Data log invalid - skipping send");
    return;
  }

  unsigned long recordTime = millis();

  StaticJsonDocument<200> doc;
  doc["type"] = "data_log";  // Loại dữ liệu cho chi tiết và đồ thị
  doc["Uc"] = Uc;
  doc["I"] = current;
  doc["heater"] = deviceData.heaterState;
  doc["fan"] = deviceData.fanState;
  doc["recordTime"] = recordTime;

  String json;
  size_t jsonSize = serializeJson(doc, json);
  if (jsonSize > 0) {
    webSocket.sendTXT(json);
    Serial.printf("[Data Log 0.1Hz] Uc=%.3fmV, I=%.3fuA logged at %lu ms (JSON: %d bytes)\n",
                  Uc, current, recordTime, jsonSize);
  } else {
    Serial.println("[LỖI] Không thể tạo JSON cho data log!");
  }

  // FIX RACE CONDITION: Reset flag
  isMeasuring = false;
}



void setup() {
  Serial.begin(115200);

  pinMode(POWER_PIN, OUTPUT);
  pinMode(HEATER_PIN, OUTPUT);
  pinMode(FAN_PIN, OUTPUT);
  // Khởi tạo tất cả GPIO ở LOW (trạng thái ban đầu tắt)
  digitalWrite(POWER_PIN, LOW);
  digitalWrite(HEATER_PIN, LOW);
  digitalWrite(FAN_PIN, LOW);

  // Cấu hình DAC cho chân GPIO25: tạo tín hiệu khoảng 100 mV
  dacWrite(DAC_PIN, 8); // Tính theo: 3.3V * 8/255 ≈ 0.1035V

  if (!ads.begin()) {
    Serial.println("Lỗi khởi tạo ADS1115!");
    while (1);
  }
  // Cấu hình cho cả micro ammeter và thermocouple
  ads.setGain(GAIN_SIXTEEN); // ±0.256V, 7.8125µV/bit - tối ưu cho thermocouple 0-35mV và micro ammeter 0-50µA

  // Validation cấu hình cho thí nghiệm Curie
  

  // Đặt lại bộ lọc khi khởi động
  boLocDong.reset(0);
  boLocDienAp.reset(0);
  Serial.println("[KHOI_DONG] Đã đặt lại bộ lọc khi khởi động");
  WiFi.begin(ssid, password);
  Serial.print("Đang kết nối WiFi...");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nĐã kết nối WiFi!");

  webSocket.begin(ws_host, ws_port, ws_url);
  webSocket.onEvent(handleWebSocketEvent);
  webSocket.setReconnectInterval(5000);
}

void loop() {
  // Kiểm tra heap memory để phát hiện memory leak
  static unsigned long lastMemoryCheck = 0;
  unsigned long currentTime = millis();

  if (currentTime - lastMemoryCheck >= 30000) { // Kiểm tra mỗi 30s
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 10000) { // Cảnh báo nếu heap < 10KB
      Serial.printf("[CẢNH BÁO] Heap thấp: %d bytes\n", freeHeap);
    }
    lastMemoryCheck = currentTime;
  }

  webSocket.loop();

  if (isConnected && deviceData.powerState) {
    // Gửi dữ liệu khi có nguồn, không cần bật bếp/quạt

    // Gửi dữ liệu real-time mỗi 100ms (10Hz) cho index
    if (currentTime - lastSensorUpdate >= SENSOR_INTERVAL) {
      sendRealtimeData();
      lastSensorUpdate = currentTime;
    }

    // Gửi dữ liệu chi tiết mỗi 10s (0.1Hz) cho logging và đồ thị
    if (currentTime - lastDataLog >= DATA_LOG_INTERVAL) {
      sendDataLog();
      lastDataLog = currentTime;
    }
  }
}
