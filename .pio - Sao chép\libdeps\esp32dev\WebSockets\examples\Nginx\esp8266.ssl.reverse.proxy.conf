# ESP8266 nginx SSL reverse proxy configuration file (tested and working on nginx v1.10.0)

# proxy cache location
proxy_cache_path /opt/etc/nginx/cache levels=1:2 keys_zone=ESP8266_cache:10m max_size=10g inactive=5m use_temp_path=off;

# webserver proxy
server {

    # general server parameters
    listen                      50080;
    server_name                 myDomain.net;
    access_log                  /opt/var/log/nginx/myDomain.net.access.log;       

    # SSL configuration
    ssl                         on;
    ssl_certificate             /usr/builtin/etc/certificate/lets-encrypt/myDomain.net/fullchain.pem;
    ssl_certificate_key         /usr/builtin/etc/certificate/lets-encrypt/myDomain.net/privkey.pem;
    ssl_session_cache           builtin:1000  shared:SSL:10m;
    ssl_protocols               TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers                 HIGH:!aNULL:!eNULL:!EXPORT:!CAMELLIA:!DES:!MD5:!PSK:!RC4;
    ssl_prefer_server_ciphers   on;
    
    location / {

      # proxy caching configuration
      proxy_cache             ESP8266_cache;
      proxy_cache_revalidate  on;
      proxy_cache_min_uses    1;
      proxy_cache_use_stale   off;
      proxy_cache_lock        on;
      # proxy_cache_bypass      $http_cache_control;      
      # include the sessionId cookie value as part of the cache key - keeps the cache per user
      # proxy_cache_key         $proxy_host$request_uri$cookie_sessionId;

      # header pass through configuration
      proxy_set_header        Host $host;      
      proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header        X-Forwarded-Proto $scheme;      

      # ESP8266 custom headers which identify to the device that it's running through an SSL proxy     
      proxy_set_header        X-SSL On;
      proxy_set_header        X-SSL-WebserverPort 50080;
      proxy_set_header        X-SSL-WebsocketPort 50081;

      # extra debug headers      
      add_header              X-Proxy-Cache $upstream_cache_status;
      add_header              X-Forwarded-For $proxy_add_x_forwarded_for;

      # actual proxying configuration
      proxy_ssl_session_reuse on;
      # target the IP address of the device with proxy_pass
      proxy_pass              http://************;
      proxy_read_timeout      90;
    }
 }

# websocket proxy
server {

    # general server parameters
    listen                      50081;
    server_name                 myDomain.net;
    access_log                  /opt/var/log/nginx/myDomain.net.wss.access.log;

    # SSL configuration
    ssl                         on;
    ssl_certificate             /usr/builtin/etc/certificate/lets-encrypt/myDomain.net/fullchain.pem;
    ssl_certificate_key         /usr/builtin/etc/certificate/lets-encrypt/myDomain.net/privkey.pem;
    ssl_session_cache           builtin:1000  shared:SSL:10m;
    ssl_protocols               TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers                 HIGH:!aNULL:!eNULL:!EXPORT:!CAMELLIA:!DES:!MD5:!PSK:!RC4;
    ssl_prefer_server_ciphers   on;
    
    location / {     

      # websocket upgrade tunnel configuration
      proxy_pass                    http://************:81;
      proxy_http_version            1.1;
      proxy_set_header Upgrade      $http_upgrade;
      proxy_set_header Connection   "Upgrade";
      proxy_read_timeout            86400;
    }
 }
